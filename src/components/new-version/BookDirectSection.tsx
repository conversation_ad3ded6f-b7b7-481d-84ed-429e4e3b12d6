'use client';

import React from 'react';
import {
  Box,
  Typography,
  Container,
  Stack,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper,
} from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import Image from 'next/image';
import BookDirectImage from '@/assets/images/<EMAIL>';

const BookDirectSection = () => (
  <Box
    sx={{
      width: '100%',
      bgcolor: 'white',
      py: 10,
      px: 2,
    }}
  >
    <Typography
      variant='h2'
      sx={{
        fontSize: { xs: '1.875rem', md: '2.25rem' },
        fontWeight: 'bold',
        mb: 4,
        textAlign: 'center',
        width: '100%',
      }}
    >
      Book Direct with Us and Get
    </Typography>
    <Container maxWidth='xl'>
      <Stack
        direction={{ xs: 'column', md: 'row' }}
        spacing={{ xs: 6, md: 10 }}
        alignItems='center'
        sx={{ gap: { xs: 2, md: 10 } }}
      >
        {/* Left: Image */}
        <Box sx={{ flex: 1, display: 'flex', justifyContent: 'right' }}>
          <Paper
            elevation={8}
            sx={{
              borderRadius: 2.5,
              overflow: 'hidden',
              width: '100%',
              maxWidth: 400,
              height: 320,
              boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.10)',
            }}
          >
            <Image
              src={BookDirectImage}
              alt='Book Direct Mobile'
              width={400}
              height={320}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
              }}
            />
          </Paper>
        </Box>

        {/* Right: Benefits List */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start',
            justifyContent: 'center',
            marginLeft: '0px !important',
            marginTop: '0px !important',
          }}
        >
          <List sx={{ width: '100%' }}>
            {[
              'Lowest Rate Guarantee',
              'Free Cancellation',
              'Flexible Date Changes',
              'Hassle-Free Booking Transfer',
              'Free Room Upgrade',
              '1-Hour Late Checkout - Free',
              'Free 1-Hour Lounge Access at MAX',
            ].map((item) => (
              <ListItem key={item} sx={{ px: 0, py: 0.5 }}>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <CheckIcon sx={{ color: '#808AC4', fontSize: 24 }} />
                </ListItemIcon>
                <ListItemText
                  primary={item}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontSize: '1.125rem',
                      color: '#434a5e',
                      fontWeight: 500,
                    },
                  }}
                />
              </ListItem>
            ))}
            <ListItem sx={{ px: 0, py: 0.5 }}>
              <ListItemIcon sx={{ minWidth: 36 }}>
                <CheckIcon sx={{ color: '#808AC4', fontSize: 24 }} />
              </ListItemIcon>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <span>Free Lounge Entry</span>
                    <Box
                      sx={{
                        bgcolor: '#181589',
                        color: 'white',
                        fontSize: '0.75rem',
                        fontWeight: 'bold',
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                      }}
                    >
                      MAX ONLY
                    </Box>
                  </Box>
                }
                sx={{
                  '& .MuiListItemText-primary': {
                    fontSize: '1.125rem',
                    color: '#434a5e',
                    fontWeight: 500,
                  },
                }}
              />
            </ListItem>
          </List>
        </Box>
      </Stack>
    </Container>
  </Box>
);

export default BookDirectSection;
