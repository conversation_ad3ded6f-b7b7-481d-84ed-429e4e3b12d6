'use client';

import React, { useMemo, useState } from 'react';
import {
  Box,
  Grid,
  Typography,
  IconButton,
  CircularProgress,
} from '@mui/material';
import {
  ArrowBackIos as ArrowLeftIcon,
  ArrowForwardIos as ArrowRightIcon,
} from '@mui/icons-material';
import useGetRoomImages from '@/hooks/useGetRoomImages';
import { OUTLETS } from '@/constant/outlet';
import GalleryListModal from './GalleryListModal';
import ModalFullISingleImage from './ModalFullISingleImage';
import {
  RoomImagesApiResponse,
  TransformImage,
} from '@/actions/getRoomImages/types';
import { getImagesByOutlet } from '@/actions/getRoomImages';
import { Outlet } from '@/actions/getOutlets/types';

const GalleryImages = ({
  outlet,
  roomImageData,
}: {
  outlet: {
    key: string;
    lotId: number;
  } & { [k: string]: any };
  roomImageData: RoomImagesApiResponse;
}) => {
  const [selectedImage, setSelectedImage] = useState<TransformImage | null>(
    null
  );
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showGalleryModal, setShowGalleryModal] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);
  const [mobileCurrentIndex, setMobileCurrentIndex] = useState(0);
  const { data: resultRoomImage, isLoading } = useGetRoomImages({
    isAutoFetch: !roomImageData?.data?.length,
  });

  console.log('isLoading', isLoading);
  console.log('roomImageData', roomImageData);

  const groupImagesByType = useMemo(() => {
    return (
      roomImageData?.groupImagesByType ||
      resultRoomImage?.groupImagesByType ||
      {}
    );
  }, [roomImageData?.groupImagesByType, resultRoomImage?.groupImagesByType]);

  const imageCategories = useMemo(() => {
    const imageByOutlet = groupImagesByType[String(outlet?.key)?.toLowerCase()];

    if (!imageByOutlet) return [];

    const categories = [];
    let countTotal = 0;
    for (const prop in imageByOutlet) {
      countTotal += imageByOutlet[prop].images.length || 0;
      categories.push({
        name: prop,
        label: prop
          .split(' ')
          .map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          )
          .join(' '),
        count: imageByOutlet[prop].images.length,
        outlet: imageByOutlet[prop].outlet,
        roomType: imageByOutlet[prop].roomType,
      });
    }

    categories.unshift({
      name: 'All',
      label: 'All',
      count: countTotal,
      outlet: outlet?.key,
    });

    return categories;
  }, [groupImagesByType, outlet?.key]);

  const imagesByOutlet = getImagesByOutlet(
    groupImagesByType,
    String(outlet?.key)?.toLowerCase()
  );

  console.log('outlet', outlet);
  console.log('imagesByOutlet', imagesByOutlet);

  const handleImageClick = (image: TransformImage, index: number) => {
    setSelectedImage(image);
    setCurrentImageIndex(index);
  };

  const handleClose = () => {
    setSelectedImage(null);
  };

  const handlePrevious = () => {
    const newIndex =
      currentImageIndex === 0
        ? imagesByOutlet.length - 1
        : currentImageIndex - 1;
    setCurrentImageIndex(newIndex);
    setSelectedImage(imagesByOutlet[newIndex]);
  };

  const handleNext = () => {
    const newIndex =
      currentImageIndex === imagesByOutlet.length - 1
        ? 0
        : currentImageIndex + 1;
    setCurrentImageIndex(newIndex);
    setSelectedImage(imagesByOutlet[newIndex]);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
    setCurrentImageIndex(0);
    setMobileCurrentIndex(0);
  };

  const getFilteredImages = () => {
    const selectedCategory = imageCategories[selectedTab]?.name;

    if (selectedCategory === 'All') {
      return imagesByOutlet;
    }

    const imageByOutletCategory =
      groupImagesByType[String(outlet?.key)?.toLowerCase()];

    return imageByOutletCategory?.[selectedCategory]?.images || [];
  };

  const filteredImages = getFilteredImages();

  const handleMobilePrevious = () => {
    const newIndex =
      mobileCurrentIndex === 0
        ? filteredImages.length - 1
        : mobileCurrentIndex - 1;
    setMobileCurrentIndex(newIndex);
  };

  const handleMobileNext = () => {
    const newIndex =
      mobileCurrentIndex === filteredImages.length - 1
        ? 0
        : mobileCurrentIndex + 1;
    setMobileCurrentIndex(newIndex);
  };

  return !outlet || !imagesByOutlet?.length ? (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        padding: '50px',
      }}
    >
      {isLoading ? (
        <CircularProgress />
      ) : (
        <Typography variant='h6'>No Images</Typography>
      )}
    </Box>
  ) : (
    <div>
      {/* Desktop Gallery Grid - Hidden on mobile */}
      <Box
        sx={{
          maxWidth: 1376,
          mx: 'auto',
          mt: '24px',
          px: { xs: 2, md: 3 },
          display: { xs: 'none', md: 'block' },
        }}
      >
        <Grid container spacing={3} sx={{ height: 430 }}>
          {/* Left side - Main image */}
          <Grid
            item
            xs={12}
            lg={6}
            md={6}
            sx={{
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              paddingTop: '0 !important',
              paddingLeft: '0 !important',
              paddingRight: '12px',
            }}
          >
            <Box
              sx={{
                position: 'relative',
                width: '100%',
                height: 430,
                cursor: 'pointer',
                overflow: 'hidden',
                borderRadius: 2,
                '&:hover .image-overlay': {
                  opacity: 1,
                },
              }}
              onClick={() => handleImageClick(imagesByOutlet[0], 0)}
            >
              <img
                src={
                  imagesByOutlet[0]?.formats?.large ||
                  imagesByOutlet[0]?.formats?.medium ||
                  imagesByOutlet[0]?.url
                }
                alt={imagesByOutlet[0]?.name}
                style={{ objectFit: 'cover', width: '100%', height: '100%' }}
                sizes='(max-width: 768px) 100vw, 570px'
              />
              <Box
                className='image-overlay'
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(0, 0, 0, 0.3)',
                  opacity: 0,
                  transition: 'opacity 0.3s ease',
                }}
              />
            </Box>
          </Grid>

          {/* Right side - Grid of smaller images */}
          <Grid
            item
            xs={12}
            lg={6}
            md={6}
            sx={{
              height: '100%',
              paddingLeft: '0 !important',
              paddingTop: '0 !important',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: '12px',
                height: '100%',
                width: '100%',
                overflow: 'hidden',
              }}
            >
              {/* First row - Responsive grid */}
              <Box sx={{ display: 'flex', gap: '12px', flexWrap: 'nowrap' }}>
                {/* Image 1 - Always visible */}
                {imagesByOutlet[1] && (
                  <Box
                    sx={{
                      position: 'relative',
                      flex: 1,
                      height: 210,
                      cursor: 'pointer',
                      overflow: 'hidden',
                      borderRadius: 2,
                      '&:hover .image-overlay': {
                        opacity: 1,
                      },
                    }}
                    onClick={() => handleImageClick(imagesByOutlet[1], 1)}
                  >
                    <img
                      src={
                        imagesByOutlet[1]?.formats?.small ||
                        imagesByOutlet[1]?.url
                      }
                      alt={imagesByOutlet[1]?.name}
                      style={{
                        objectFit: 'cover',
                        width: '100%',
                        height: '100%',
                      }}
                      sizes='(max-width: 1200px) 210px, 260px'
                    />
                    <Box
                      className='image-overlay'
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.3)',
                        opacity: 0,
                        transition: 'opacity 0.3s ease',
                      }}
                    />
                  </Box>
                )}

                {/* Image 3 - Visible on lg and up */}
                {imagesByOutlet[3] && (
                  <Box
                    sx={{
                      position: 'relative',
                      flex: 1,
                      height: 210,
                      cursor: 'pointer',
                      overflow: 'hidden',
                      borderRadius: 2,
                      display: { xs: 'none', md: 'block' },
                      '&:hover .image-overlay': {
                        opacity: 1,
                      },
                    }}
                    onClick={() => handleImageClick(imagesByOutlet[3], 3)}
                  >
                    <img
                      src={
                        imagesByOutlet[3]?.formats?.small ||
                        imagesByOutlet[3]?.url
                      }
                      alt={imagesByOutlet[3]?.name}
                      style={{
                        objectFit: 'cover',
                        width: '100%',
                        height: '100%',
                      }}
                      sizes='(max-width: 1200px) 210px, 260px'
                    />
                    <Box
                      className='image-overlay'
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.3)',
                        opacity: 0,
                        transition: 'opacity 0.3s ease',
                      }}
                    />
                  </Box>
                )}

                {/* Image 5 - Only visible on xl */}
                {imagesByOutlet[5] && (
                  <Box
                    sx={{
                      position: 'relative',
                      flex: 1,
                      height: 210,
                      cursor: 'pointer',
                      overflow: 'hidden',
                      borderRadius: 2,
                      display: { xs: 'none', lg: 'block' },
                      '&:hover .image-overlay': {
                        opacity: 1,
                      },
                    }}
                    onClick={() => handleImageClick(imagesByOutlet[5], 5)}
                  >
                    <img
                      src={
                        imagesByOutlet[5]?.formats?.small ||
                        imagesByOutlet[5]?.url
                      }
                      alt={imagesByOutlet[5]?.name}
                      style={{
                        objectFit: 'cover',
                        width: '100%',
                        height: '100%',
                      }}
                      sizes='260px'
                    />
                    <Box
                      className='image-overlay'
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.3)',
                        opacity: 0,
                        transition: 'opacity 0.3s ease',
                      }}
                    />
                  </Box>
                )}
              </Box>

              {/* Second row - Responsive grid */}
              <Box sx={{ display: 'flex', gap: '12px', flexWrap: 'nowrap' }}>
                {/* Image 2 - Always visible */}
                {imagesByOutlet[2] && (
                  <Box
                    sx={{
                      position: 'relative',
                      flex: 1,
                      height: 210,
                      cursor: 'pointer',
                      overflow: 'hidden',
                      borderRadius: 2,
                      '&:hover .image-overlay': {
                        opacity: 1,
                      },
                    }}
                    onClick={() => handleImageClick(imagesByOutlet[2], 2)}
                  >
                    <img
                      src={
                        imagesByOutlet[2]?.formats?.small ||
                        imagesByOutlet[2]?.url
                      }
                      alt={imagesByOutlet[2]?.name}
                      style={{
                        objectFit: 'cover',
                        width: '100%',
                        height: '100%',
                      }}
                      sizes='(max-width: 1200px) 210px, 260px'
                    />
                    <Box
                      className='image-overlay'
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.3)',
                        opacity: 0,
                        transition: 'opacity 0.3s ease',
                      }}
                    />
                  </Box>
                )}

                {/* Image 4 - Visible on lg and up */}
                {imagesByOutlet[4] && (
                  <Box
                    sx={{
                      position: 'relative',
                      flex: 1,
                      height: 210,
                      cursor: 'pointer',
                      overflow: 'hidden',
                      borderRadius: 2,
                      display: { xs: 'none', lg: 'block' },
                      '&:hover .image-overlay': {
                        opacity: 1,
                      },
                    }}
                    onClick={() => handleImageClick(imagesByOutlet[4], 4)}
                  >
                    <img
                      src={
                        imagesByOutlet[4]?.formats?.small ||
                        imagesByOutlet[4]?.url
                      }
                      alt={imagesByOutlet[4]?.name}
                      style={{
                        objectFit: 'cover',
                        width: '100%',
                        height: '100%',
                      }}
                      sizes='(max-width: 1200px) 210px, 260px'
                    />
                    <Box
                      className='image-overlay'
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.3)',
                        opacity: 0,
                        transition: 'opacity 0.3s ease',
                      }}
                    />
                  </Box>
                )}

                {/* + More photos button - Always visible */}
                {imagesByOutlet?.length > 7 && (
                  <Box
                    sx={{
                      position: 'relative',
                      flex: 1,
                      height: 210,
                      cursor: 'pointer',
                      overflow: 'hidden',
                      borderRadius: 2,
                      backgroundColor: 'black',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      '&:hover .image-overlay': {
                        opacity: 1,
                      },
                    }}
                    onClick={() => setShowGalleryModal(true)}
                  >
                    <Typography
                      variant='h6'
                      sx={{
                        color: 'white',
                        fontWeight: 600,
                        textAlign: 'center',
                        fontSize: { xl: '1.25rem', lg: '1.125rem', md: '1rem' },
                      }}
                    >
                      + {imagesByOutlet?.length - 7} photos
                    </Typography>
                    <Box
                      className='image-overlay'
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.3)',
                        opacity: 0,
                        transition: 'opacity 0.3s ease',
                      }}
                    />
                  </Box>
                )}
                {imagesByOutlet?.length === 7 && (
                  <Box
                    sx={{
                      position: 'relative',
                      flex: 1,
                      height: 210,
                      cursor: 'pointer',
                      overflow: 'hidden',
                      borderRadius: 2,
                      backgroundColor: 'black',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      '&:hover .image-overlay': {
                        opacity: 1,
                      },
                    }}
                    onClick={() => handleImageClick(imagesByOutlet[6], 6)}
                  >
                    <img
                      src={
                        imagesByOutlet[6]?.formats?.small ||
                        imagesByOutlet[6]?.url
                      }
                      alt={imagesByOutlet[6]?.name}
                      style={{
                        objectFit: 'cover',
                        width: '100%',
                        height: '100%',
                      }}
                      sizes='(max-width: 1200px) 210px, 260px'
                    />
                    <Box
                      className='image-overlay'
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.3)',
                        opacity: 0,
                        transition: 'opacity 0.3s ease',
                      }}
                    />
                  </Box>
                )}
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Mobile Slider - Only visible on mobile */}
      <Box
        sx={{
          display: { xs: 'block', md: 'none' },
          maxWidth: '100%',
          mx: 'auto',
        }}
      >
        <Box
          sx={{
            position: 'relative',
            width: '100%',
            height: 321,
            overflow: 'hidden',
            borderRadius: 2,
            cursor: 'pointer',
          }}
        >
          {/* Current Image */}
          <Box
            sx={{
              position: 'relative',
              width: '100%',
              height: '100%',
            }}
          >
            <img
              src={
                filteredImages[mobileCurrentIndex]?.url ||
                imagesByOutlet[0]?.url
              }
              alt={
                filteredImages[mobileCurrentIndex]?.name ||
                imagesByOutlet[0]?.name
              }
              style={{ objectFit: 'cover', width: '100%', height: '100%' }}
              sizes='100vw'
            />
          </Box>

          {/* Left Navigation Button */}
          <IconButton
            onClick={handleMobilePrevious}
            sx={{
              position: 'absolute',
              left: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 10,
              backgroundColor: 'rgba(15, 14, 14, 0.5)',
              width: '32px',
              height: '48px',
              color: 'white',
              transition: 'all 0.2s',
              borderRadius: '4px',
              paddingLeft: '16px',
              '&:hover': {
                backgroundColor: 'rgba(15, 14, 14, 0.8)',
              },
            }}
          >
            <ArrowLeftIcon />
          </IconButton>

          {/* Right Navigation Button */}
          <IconButton
            onClick={handleMobileNext}
            sx={{
              position: 'absolute',
              right: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 10,
              backgroundColor: 'rgba(15, 14, 14, 0.5)',
              width: '32px',
              height: '48px',
              color: 'white',
              transition: 'all 0.2s',
              borderRadius: '4px',
              '&:hover': {
                backgroundColor: 'rgba(15, 14, 14, 0.8)',
              },
            }}
          >
            <ArrowRightIcon />
          </IconButton>

          {/* Image Counter */}
          <Typography
            variant='body2'
            sx={{
              position: 'absolute',
              bottom: 16,
              left: 16,
              bgcolor: 'rgba(0, 0, 0, 0.7)',
              color: 'white !important',
              px: 2,
              py: 1,
              borderRadius: 1,
              fontSize: '14px !important',
              fontWeight: 500,
              transition: 'all 0.3s ease-in-out',
              backdropFilter: 'blur(4px)',
            }}
            onClick={() => setShowGalleryModal(true)}
          >
            {mobileCurrentIndex + 1} / {filteredImages.length}
          </Typography>
        </Box>
      </Box>

      {/* Gallery Modal with Tabs */}
      <GalleryListModal
        showGalleryModal={showGalleryModal}
        setShowGalleryModal={setShowGalleryModal}
        imageCategories={imageCategories}
        filteredImages={filteredImages}
        selectedTab={selectedTab}
        handleTabChange={handleTabChange}
      />

      {/* Modal for full-size image view */}
      <ModalFullISingleImage
        selectedImage={selectedImage}
        handleClose={handleClose}
        handlePrevious={handlePrevious}
        handleNext={handleNext}
        currentImageIndex={currentImageIndex}
        filteredImages={imagesByOutlet}
      />
    </div>
  );
};

export default GalleryImages;
