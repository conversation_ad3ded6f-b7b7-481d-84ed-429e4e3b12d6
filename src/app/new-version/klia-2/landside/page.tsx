'use server';

import React from 'react';
import HeroSection from '@/components/new-version/HeroSection';
import Layout from '@/components/new-version/Layout';
import Gallery from '@/components/new-version/Gallery';
import FacilitiesAndAmenities from '@/components/new-version/FacilitiesAndAmenities';
import RoomOptions from '@/components/new-version/RoomOptions';
import GuestReviews from '@/components/new-version/GuestReviews';
import GuideKliaT2 from '@/components/new-version/GuideKliaT2';
import BookYourReservation from '@/components/new-version/BookYourReservation';
import { getOutlets } from '@/actions/getOutlets';
import { getReviews } from '@/actions/getReviews';

async function Klia2LandsidePage() {
  const [outlets, reviews] = await Promise.all([
    getOutlets(),
    getReviews(),
  ]);

  const currentOutlet = outlets.find((outlet) => outlet.key === 'Landside');

  return (
    <Layout>
      <HeroSection
        outlets={currentOutlet ? [currentOutlet] : outlets}
        lotId={currentOutlet?.lotId}
      />
      <Gallery lotId={currentOutlet?.lotId} />
      <FacilitiesAndAmenities />
      <RoomOptions selectedOutlet={currentOutlet} />
      <GuestReviews reviewsData={reviews['data']} />
      <GuideKliaT2 currentOutlet={currentOutlet} />
      <BookYourReservation
        outletName='CapsuleTransit Landside'
        selectedOutlet={currentOutlet}
      />
    </Layout>
  );
}

export default Klia2LandsidePage;
