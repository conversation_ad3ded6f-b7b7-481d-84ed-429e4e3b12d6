'use client';

import {
  useCallback,
  useState,
  useRef,
  useLayoutEffect,
  useEffect,
} from 'react';
import { Alert, Box, Snackbar } from '@mui/material';
import axios from 'axios';
import FilterRoomsSection from '@/components/new-version/FilterRoomsSection';
import RoomsSelectionList from '@/components/new-version/RoomsSelection';
import FacilitiesAndAmenities from '@/components/new-version/FacilitiesAndAmenities';
import GalleryImages from '@/components/new-version/Gallery/GalleryImages';
import CapsuleTransitHeading from '@/components/new-version/CapsuleTransitHeading';
import GuideKliaT2 from '@/components/new-version/GuideKliaT2';
import useGetRooms from './hooks/useGetRooms';
import SelectedRoom from '@/components/new-version/SelectedRoom';
import NavbarNewBooking from '@/components/new-version/NavbarNewBooking';
import { FilterData } from '@/components/new-version/FilterRoomsSection/types';
import { OUTLETS, PriceOption } from '@/constant/outlet';
import BookingPayment from '@/components/new-version/BookingPayment';
import useGetHotelDetail from '@/hooks/useGetHotelDetail';
import useGetTaxAndServiceChargeByLot from '@/hooks/useGetTaxAndServiceChargeByLot';
import useGetGuestsCountry from '@/hooks/useGetGuestsCountry';
import { FormPaymentData } from './types/form-payment-data';
import { API_BASE_URL } from '@/config/app';
import { useBookingData } from '@/context/BookingContext';
import { IBookingInformation } from '@/models/Booking';
import { DURATIONS } from '@/constant/time-selection';
import Footer from '@/components/new-version/Footer';
import { RoomsSelectionProvider } from '@/components/new-version/RoomsSelection/context';
import { SelectedRooms } from '@/components/new-version/RoomsSelection/types';
import { RoomImagesApiResponse } from '@/actions/getRoomImages/types';

const BookingPageClient = ({
  roomImageData,
}: {
  roomImageData: RoomImagesApiResponse;
}) => {
  const { setBookingData } = useBookingData();
  const [errorNotification, setErrorNotification] = useState<string>('');

  const handleOpenErrorBooking = (text: string) => {
    setErrorNotification(text);
  };

  const handleCloseErrorBooking = () => {
    setErrorNotification('');
  };
  const [selectedRooms, setSelectedRooms] = useState<SelectedRooms>({});
  const [filters, setFilters] = useState<FilterData>({});
  const roomsSelectionRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const onApplyFilter = useCallback((filterData: FilterData) => {
    console.log('onApplyFilter called with:', filterData);
    setFilters(filterData);
    // reset selected Rooms when filters changed
    setSelectedRooms({});

    // Clear any existing scroll timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Check if room selection is already visible before scrolling
    scrollTimeoutRef.current = setTimeout(() => {
      console.log('Checking if room selection is visible');
      if (roomsSelectionRef.current) {
        // Wait for next frame to ensure DOM is fully rendered
        requestAnimationFrame(() => {
          if (roomsSelectionRef.current) {
            const rect = roomsSelectionRef.current.getBoundingClientRect();
            const navbarHeight = window.innerWidth >= 900 ? 76 : 72;
            const filterSectionHeight = window.innerWidth >= 900 ? 120 : 0;
            const totalFixedHeight = navbarHeight + filterSectionHeight;

            // Check if the "Select Your Option" title is visible
            // We consider it visible if the top of the element is below the fixed elements
            // and at least part of it is within the viewport
            const isVisible = rect.top >= totalFixedHeight && rect.top < window.innerHeight;

            console.log('Visibility check:', {
              elementTop: rect.top,
              totalFixedHeight,
              windowHeight: window.innerHeight,
              isVisible
            });

            if (isVisible) {
              console.log('Room selection is already visible, skipping scroll');
              return;
            }

            console.log('Room selection not visible, scrolling to it');
            // Calculate the offset needed to account for fixed header and filter section
            const totalOffset = totalFixedHeight + 20; // Add 20px padding

            // Get the element's position
            const elementTop = roomsSelectionRef.current.getBoundingClientRect().top + window.pageYOffset;
            const offsetPosition = elementTop - totalOffset;

            console.log('Scroll calculation:', {
              navbarHeight,
              filterSectionHeight,
              totalOffset,
              elementTop,
              offsetPosition,
              currentScrollY: window.pageYOffset
            });

            // Scroll to the calculated position
            window.scrollTo({
              top: offsetPosition,
              behavior: 'smooth'
            });
          }
        });
      }
      scrollTimeoutRef.current = null;
    }, 500); // Increased delay to ensure DOM updates are complete
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    // get offset top of roomsSelectionRef.current when scrolling down fired
    if (roomsSelectionRef.current) {
      const handleScroll = () => {
        const offsetTop = roomsSelectionRef.current?.offsetTop;
        console.log('offsetTop', offsetTop);
      };

      window.addEventListener('scroll', handleScroll);

      // Cleanup function to remove event listener
      return () => {
        window.removeEventListener('scroll', handleScroll);
      };
    }
  }, [filters]);

  const { data: hotelDetail } = useGetHotelDetail({
    lotId: Number(filters.lotId),
  });

  const { data: taxAndServiceCharge } = useGetTaxAndServiceChargeByLot({
    lotId: Number(filters.lotId),
  });

  const { data: guestsCountry } = useGetGuestsCountry();

  const {
    rooms,
    isLoading: roomsLoading,
    roomPriceOptions: otherAvailableRoomOptions,
    isLoadingRoomsByDurations: isLoadingOtherAvailableRoomOptions,
  } = useGetRooms({
    checkInDatetime: filters.checkInDatetime || 0,
    duration: filters.stayDuration || 0,
    lotId: filters.lotId ? parseInt(filters.lotId) : 0,
    durations: DURATIONS.map((duration) => duration.value),
  });

  const handleSelectRoom = ({
    room,
    quantity,
  }: {
    room: any;
    quantity: number;
  }) => {
    setSelectedRooms((prev) => ({
      ...prev,
      [room.name]: {
        room,
        quantity,
      },
    }));
  };

  const handleRemoveRoom = (room: any) => {
    setSelectedRooms((prev) => {
      const rooms = { ...prev };

      delete rooms[room.name];

      return rooms;
    });
  };

  const handleChangeOtherAvailableOption = (
    room: any,
    selectedOption: PriceOption
  ) => {
    setFilters((prev) => ({
      ...prev,
      stayDuration: selectedOption.hours,
    }));
    // since changing price option, the selected room should be reset
    if (Object.keys(selectedRooms)) {
      setSelectedRooms((prev) => {
        const rooms = { ...prev };

        if (rooms[room.name]) {
          // change the duration of selected room
          return {
            [room.name]: {
              ...rooms[room.name],
              room: {
                ...room,
                selectedOption: selectedOption,
              },
            },
          };
        }

        // reset other selection after change duration
        return {};
      });
    }
  };

  // Transform selectedRooms to the format expected by SelectedRoom component
  const transformSelectedRooms = () => {
    const transformedRooms = Object.values(selectedRooms).map((item: any) => ({
      id: item.room.id,
      room: item.room,
      bedType: item.room.bedType,
      roomType: item.room.name,
      roomName: item.room.name,
      quantity: item.quantity,
      zone: item.room.zone,
      duration: item.room.selectedOption.hours || filters.stayDuration,
      price: item.room.selectedOption.price || item.room.price,
      imageUrl: item.room.images?.[0]?.url ? [item.room.images?.[0]?.url] : [],
    }));
    return transformedRooms;
  };

  const [clickedBookNow, setClickedBookNow] = useState<boolean>(false);

  const onHandlePayment = async (formData: FormPaymentData) => {
    try {
      const bodyData = {
        lotId: hotelDetail.lotId,
        checkinDatetime: filters.checkInDatetime,
        duration: filters.stayDuration,
        roomTypes: transformSelectedRooms().map((r) => ({
          roomTypeName: r.roomType,
          quantity: r.quantity,
          roomPrice: r.price,
          duration: r.duration,
        })),
        ...formData,
      };

      const result = await axios
        .post(`${API_BASE_URL}/landing-page/booking/`, bodyData, {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
        })
        .then((res) => res.data.data || {});

      const bookingData = {
        guestDetail: formData,
        payment: formData,
        roomBookings: transformSelectedRooms(),
        selectedHotel: hotelDetail,
        bookingSchedule: filters,
        bookingNo: result.bookingNo,
        bookingId: result.bookingId,
      };

      setBookingData(bookingData as unknown as IBookingInformation);

      const formatter = new Intl.DateTimeFormat('en-US', {
        timeZone: 'Asia/Singapore',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false, // Use 24-hour format
      });

      const singaporeTime = formatter.format(filters.date as Date);

      const iPay88Data = {
        amount: bodyData.creditAmount,
        refNo: result?.bookingId,
        bookingNo: `${result?.bookingNo}, Check In Date ${singaporeTime}`,
        userContact: bodyData.phoneNumber,
        userEmail: bodyData.email,
        userName: bodyData.firstName + ' ' + bodyData.lastName,
        lot: hotelDetail?.lotNumber,
      };

      const roomDescriptions = transformSelectedRooms()
        .map((r) => `${r.quantity}x ${r.roomType}`)
        .join(', ');

      const productDescription = `${iPay88Data.lot} Capsule Transit: ${roomDescriptions}`;

      const urlSearchParams = new URLSearchParams();
      urlSearchParams.append('refNo', iPay88Data.refNo);
      urlSearchParams.append('bookingNo', iPay88Data.bookingNo);
      urlSearchParams.append('amount', String(iPay88Data.amount));
      urlSearchParams.append('contact', iPay88Data.userContact);
      urlSearchParams.append('email', iPay88Data.userEmail);
      urlSearchParams.append('name', iPay88Data.userName);
      urlSearchParams.append('prodDesc', productDescription);
      urlSearchParams.append('lot', hotelDetail?.lotNumber);

      const checkoutUrl = `/new-booking/checkout?${urlSearchParams.toString()}`;

      return {
        success: true,
        data: {
          checkoutUrl,
          iPay88Data,
          ...result,
        },
      };
    } catch (error: any) {
      console.log('error', error);

      const errorMessage =
        error?.response?.data?.message || 'Something went wrong';

      handleOpenErrorBooking(errorMessage);

      return {
        success: false,
        error: error,
      };
    }
  };

  const outlet = OUTLETS.find(
    (outlet) => outlet.lotId === Number(filters.lotId)
  );

  return (
    <Box
      display={'flex'}
      width={'99vw'}
      flexDirection={'column'}
      sx={{
        overflowX: 'hidden',
        paddingTop: { xs: '76px', md: !clickedBookNow ? '230px' : '76px' },
      }}
    >
      <NavbarNewBooking
        onClose={() => {
          // go back to room selection
          setClickedBookNow(false);
        }}
      />
      <Box display={!clickedBookNow ? 'block' : 'none'}>
        <Box
          data-filter-section
          sx={{
            position: { xs: 'unset', md: 'fixed' },
            top: { xs: 'unset', md: '76px' },
            left: { xs: 'unset', md: '0' },
            right: { xs: 'unset', md: '0' },
            zIndex: 100,
          }}
        >
          <FilterRoomsSection onApplyFilter={onApplyFilter} />
        </Box>
        {filters.lotId && (
          <CapsuleTransitHeading lotId={Number(filters.lotId)} />
        )}
        <GalleryImages
          roomImageData={roomImageData}
          outlet={
            outlet as { key: string; lotId: number } & { [k: string]: any }
          }
        />
        <FacilitiesAndAmenities
          sx={{
            backgroundColor: '#fff',
          }}
        />
        <div ref={roomsSelectionRef} id='rooms-selection-list'>
          <RoomsSelectionProvider
            selectedRooms={selectedRooms}
            selectedDuration={filters.stayDuration}
            isLoadingOtherAvailableRoomOptions={
              isLoadingOtherAvailableRoomOptions
            }
            handleChangeOtherAvailableOption={handleChangeOtherAvailableOption}
            handleSelectRoom={handleSelectRoom}
            handleRemoveRoom={handleRemoveRoom}
            roomImageData={roomImageData}
          >
            <RoomsSelectionList
              rooms={rooms}
              isLoading={roomsLoading}
              lotId={Number(filters.lotId)}
              otherAvailableRoomOptions={otherAvailableRoomOptions}
            />
          </RoomsSelectionProvider>
        </div>
        <GuideKliaT2 />

        {/* Use mock data for now, later replace with transformSelectedRooms() */}
        <SelectedRoom
          selectedRooms={transformSelectedRooms()}
          onBookNow={() => setClickedBookNow(true)}
        />
      </Box>

      {clickedBookNow && (
        <BookingPayment
          selectedRooms={transformSelectedRooms()}
          hotelDetail={hotelDetail}
          taxAndServiceCharge={taxAndServiceCharge}
          checkInInformation={filters}
          onSubmit={onHandlePayment}
          guestsCountry={guestsCountry || []}
        />
      )}

      <Snackbar
        open={Boolean(errorNotification)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        autoHideDuration={4000}
        onClose={handleCloseErrorBooking}
      >
        <Alert
          onClose={handleCloseErrorBooking}
          severity='error'
          variant='filled'
          sx={{ width: '100%', fontSize: '1rem' }}
        >
          {errorNotification}
        </Alert>
      </Snackbar>
      <Footer />
    </Box>
  );
};

export default BookingPageClient;
